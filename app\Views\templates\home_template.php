<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta name="Description" content="Dakoii Echad Recruitment & Selection System" />
  <title><?= isset($title) ? $title . ' - ' : '' ?>DERS - Dakoii Echad Recruitment & Selection System</title>
  <link rel="icon" href="<?= base_url() ?>public/assets/system_img/favicon.ico" type="image/x-icon">

  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <!-- Source Sans Pro - clean, elegant, professional font -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <!-- SweetAlert2 -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

  <!-- Page specific CSS -->
  <?= $this->renderSection('css') ?>

  <style>
    :root {
      /* Primary colors from the PNG flag color scheme */
      --red: #F00F00;             /* Red from PNG flag */
      --red-dark: #D00D00;        /* Darker red for hover states */
      --yellow: #FFC20F;          /* Yellow/Gold from PNG flag */
      --yellow-dark: #E6B00E;     /* Darker yellow for hover states */
      --black: #000000;           /* Black from PNG flag */
      --gray: #BFC1C7;            /* Light gray from the palette */
      --gray-dark: #9FA1A7;       /* Darker gray for hover states */
      --white: #FFFFFF;           /* White from the palette */

      /* UI colors */
      --text-primary: #000000;    /* Primary text color */
      --text-secondary: #333333;  /* Secondary text color */
      --text-light: #FFFFFF;      /* Light text color for dark backgrounds */
      --bg-light: #FFFFFF;        /* Light background */
      --bg-dark: #000000;         /* Dark background */
    }

    /* Optimize font loading behavior */
    @font-face {
      font-family: 'Source Sans 3';
      font-display: swap;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
      scroll-behavior: smooth;
      color: var(--text-primary);
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background-color: #f8f9fa;
    }

    html {
      scroll-behavior: smooth;
    }

    h1, h2, h3, h4, h5, h6 {
      font-family: 'Source Sans 3', sans-serif;
      font-weight: 600;
      line-height: 1.3;
      color: var(--red);
    }

    /* Fix for dark text on dark background */
    .gradient-bg h1,
    .gradient-bg h2,
    .gradient-bg h3,
    .gradient-bg h4,
    .gradient-bg h5,
    .gradient-bg h6,
    .gradient-bg .text-white-75 {
      color: var(--white) !important;
    }

    p {
      font-weight: 400;
      margin-bottom: 1.5rem;
    }

    .gradient-bg {
      background:
        radial-gradient(ellipse at bottom right, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 30%, rgba(0, 0, 0, 0.3) 50%, rgba(0, 0, 0, 0.1) 70%, transparent 85%),
        linear-gradient(135deg, var(--red), var(--red-dark));
      position: relative;
      overflow: hidden;
    }

    .gradient-bg::after {
      content: '';
      position: absolute;
      bottom: -50px;
      left: 0;
      width: 100%;
      height: 100px;
      background: var(--yellow);
      transform: skewY(-2deg);
      z-index: 1;
    }

    .content-wrapper {
      position: relative;
      z-index: 2;
      padding: 3rem 0;
    }

    /* Navbar styles */
    .navbar {
      background-color: var(--black) !important;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      padding: 0.8rem 0;
    }

    .nav-link {
      color: rgba(255, 255, 255, 0.9) !important;
      transition: all 0.3s ease;
      padding: 0.6rem 1rem;
      font-weight: 500;
      position: relative;
    }

    .nav-link:hover {
      color: var(--yellow) !important;
    }

    .nav-link.active {
      color: var(--yellow) !important;
      font-weight: 600;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background: var(--red);
      transition: all 0.3s ease;
      transform: translateX(-50%);
      opacity: 0;
    }

    .nav-link:hover::after,
    .nav-link.active::after {
      width: 70%;
      opacity: 1;
    }

    /* Button styles */
    .btn {
      font-weight: 500;
      letter-spacing: 0.02em;
      padding: 0.6rem 1.5rem;
      transition: all 0.3s ease;
    }

    .btn-red {
      background-color: var(--red);
      color: white;
      border: none;
      box-shadow: 0 4px 12px rgba(240, 15, 0, 0.2);
    }

    .btn-red:hover {
      background-color: var(--red-dark);
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(240, 15, 0, 0.3);
    }

    .btn-yellow {
      background-color: var(--yellow);
      color: var(--black);
      border: none;
      box-shadow: 0 4px 12px rgba(255, 194, 15, 0.2);
    }

    .btn-yellow:hover {
      background-color: var(--yellow-dark);
      color: var(--black);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 194, 15, 0.3);
    }

    .btn-black {
      background-color: var(--black);
      color: white;
      border: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .btn-black:hover {
      background-color: #333333;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    }

    /* Card styles */
    .card {
      border: none;
      box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(240, 15, 0, 0.15);
    }

    /* Form control styles */
    input.form-control, .form-select {
      padding: 0.8rem 1.2rem;
      font-size: 1rem;
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
    }

    input.form-control:focus, .form-select:focus {
      border-color: var(--red);
      box-shadow: 0 0 0 3px rgba(240, 15, 0, 0.2);
    }

    /* Utility classes */
    .text-red { color: var(--red) !important; }
    .text-yellow { color: var(--yellow) !important; }
    .text-gray { color: var(--gray) !important; }
    .bg-red { background-color: var(--red) !important; }
    .bg-yellow { background-color: var(--yellow) !important; }
    .bg-gray { background-color: var(--gray) !important; }

    /* Legacy class names for compatibility */
    .text-navy { color: var(--red) !important; }
    .bg-navy { background-color: var(--red) !important; }
    .text-accent-red { color: var(--yellow) !important; }
    .bg-accent-red { background-color: var(--yellow) !important; }

    .hover-text-yellow:hover {
      color: var(--yellow) !important;
      transition: color 0.3s ease;
    }
  </style>
</head>

<body class="<?= isset($body_class) ? $body_class : 'bg-light' ?>">
  <!-- Navigation Menu -->
  <nav class="navbar navbar-dark navbar-expand-lg <?= isset($navbar_fixed) && $navbar_fixed ? 'fixed-top' : '' ?>">
    <div class="container d-flex justify-content-between align-items-center py-2">
      <div class="h4 mb-0 fw-bold d-flex align-items-center gap-3">
        <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="DERS Logo" style="height: 48px;">
        <span class="d-none d-md-inline text-white">DERS</span>
      </div>
      <div class="d-flex gap-3">
        <a href="<?= base_url() ?>" class="nav-link <?= (isset($menu) && $menu == 'home') ? 'active' : '' ?>">Home</a>
        <a href="<?= base_url('jobs') ?>" class="nav-link <?= (isset($menu) && $menu == 'jobs') ? 'active' : '' ?>">Jobs</a>
        <a href="<?= base_url('about') ?>" class="nav-link <?= (isset($menu) && $menu == 'about') ? 'active' : '' ?>">About</a>
        <a href="<?= base_url('applicant/login') ?>" class="nav-link">Apply</a>
        <a href="<?= base_url('login') ?>" class="nav-link">Admin</a>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="<?= isset($navbar_fixed) && $navbar_fixed ? 'pt-5' : '' ?>">
    <?= $this->renderSection('content') ?>
  </main>

  <!-- Footer Section -->
  <footer class="bg-black text-white py-5 mt-5">
    <div class="container">
      <div class="row align-items-center gy-4">
        <div class="col-md-4 text-center text-md-start">
          <a href="https://www.dakoiims.com" target="_blank" class="d-inline-block mb-3">
            <img src="<?= base_url() ?>/public/assets/system_img/dakoii-systems-logo.png" alt="Dakoii Systems Logo" class="img-fluid" style="max-height: 60px;">
          </a>
          <p class="small mb-0 text-white-50">Innovative software solutions for government and enterprise.</p>
        </div>
        <div class="col-md-4 text-center">
          <h5 class="text-yellow mb-3">Partners in Excellence</h5>
          <a href="#" class="d-inline-block mb-3">
            <img src="<?= base_url() ?>/public/assets/system_img/echad-logo.png" alt="Echad Consultancy Services Logo" class="img-fluid" style="max-height: 60px;">
          </a>
        </div>
        <div class="col-md-4 text-center text-md-end">
          <h5 class="text-yellow mb-3">Contact</h5>
          <p class="small mb-1"><i class="bi bi-envelope-fill me-2"></i> <EMAIL></p>
          <p class="small mb-3"><i class="bi bi-globe me-2"></i> www.dakoiims.com</p>
          <div class="d-flex justify-content-center justify-content-md-end gap-3">
            <a href="#" class="text-white hover-text-yellow"><i class="bi bi-facebook fs-5"></i></a>
            <a href="#" class="text-white hover-text-yellow"><i class="bi bi-twitter fs-5"></i></a>
            <a href="#" class="text-white hover-text-yellow"><i class="bi bi-linkedin fs-5"></i></a>
          </div>
        </div>
      </div>
      <hr class="my-4 border-secondary">
      <div class="row">
        <div class="col-12 text-center">
          <p class="mb-0 small">
            &copy; <?= date('Y') ?> Developed by
            <a href="https://www.dakoiims.com" class="text-yellow text-decoration-none" target="_blank"> <strong>Dakoii Systems</strong> </a>
            in collaboration with <strong>Echad Consultancy Services</strong>. <span class="align-right">Powered by <?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></span>
          </p>
        </div>
      </div>
    </div>
  </footer>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- SweetAlert Messages -->
  <?php if (session()->has('swal_icon')): ?>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      Swal.fire({
        icon: '<?= session()->getFlashdata('swal_icon') ?>',
        title: '<?= session()->getFlashdata('swal_title') ?>',
        text: '<?= session()->getFlashdata('swal_text') ?>',
        confirmButtonColor: '#F00F00'
      });
    });
  </script>
  <?php endif; ?>

  <!-- Page specific scripts -->
  <?= $this->renderSection('scripts') ?>
</body>

</html>
