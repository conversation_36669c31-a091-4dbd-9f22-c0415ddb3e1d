<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class ApplicantJobsController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
    }

    public function index()
    {
        try {
            // Get filter parameters
            $selectedOrgId = $this->request->getGet('org_id');
            $searchTerm = $this->request->getGet('search');

            // Load models
            $exerciseModel = new \App\Models\ExerciseModel();
            $orgModel = new \App\Models\DakoiiOrgModel();
            $positionsModel = new \App\Models\PositionsModel();

            // Get all organizations for filter dropdown
            $organizations = $orgModel->where('is_active', 1)->findAll();

            // Get published exercises with organization information
            $exercisesQuery = $exerciseModel->select('
                exercises.*,
                dakoii_org.org_name,
                dakoii_org.org_code,
                dakoii_org.location_lock_province,
                dakoii_org.orglogo
            ')
            ->join('dakoii_org', 'exercises.org_id = dakoii_org.id', 'left')
            ->where('exercises.status', 'published')
            ->where('dakoii_org.is_active', 1);

            // Apply organization filter
            if ($selectedOrgId) {
                $exercisesQuery->where('exercises.org_id', $selectedOrgId);
            }

            // Apply search filter
            if ($searchTerm) {
                $exercisesQuery->groupStart()
                    ->like('exercises.exercise_name', $searchTerm)
                    ->orLike('exercises.description', $searchTerm)
                    ->orLike('dakoii_org.org_name', $searchTerm)
                    ->groupEnd();
            }

            $exercises = $exercisesQuery->findAll();

            // Get position counts for each exercise
            $allJobData = [];
            foreach ($exercises as $exercise) {
                // Count positions for this exercise
                $positionCount = $positionsModel->select('COUNT(*) as count')
                    ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                    ->where('positions_groups.exercise_id', $exercise['id'])
                    ->where('positions.status', 'active')
                    ->first();

                $allJobData[] = [
                    'exercise' => $exercise,
                    'organization' => [
                        'id' => $exercise['org_id'],
                        'org_name' => $exercise['org_name'],
                        'org_code' => $exercise['org_code'],
                        'location_lock_province' => $exercise['location_lock_province'],
                        'orglogo' => $exercise['orglogo']
                    ],
                    'position_count' => $positionCount['count'] ?? 0
                ];
            }

            return view('applicant/applicant_job_openings', [
                'title' => 'Job Openings',
                'menu' => 'jobs',
                'jobData' => $allJobData,
                'organizations' => $organizations,
                'selectedOrgId' => $selectedOrgId,
                'searchTerm' => $searchTerm,
                'totalJobs' => count($allJobData),
                'filteredJobs' => count($allJobData)
            ]);

        } catch (\Exception $e) {
            // Log the error
            log_message('error', 'Error loading applicant jobs: ' . $e->getMessage());

            return view('applicant/applicant_job_openings', [
                'title' => 'Job Openings',
                'menu' => 'jobs',
                'jobData' => [],
                'organizations' => [],
                'selectedOrgId' => $selectedOrgId,
                'searchTerm' => $searchTerm,
                'totalJobs' => 0,
                'filteredJobs' => 0,
                'error' => 'Unable to load job openings. Please try again later.'
            ]);
        }
    }

    public function view($exerciseId)
    {
        // Mock exercise data based on ExerciseModel structure
        $exercises = [
            1 => [
                'id' => 1,
                'org_id' => 1,
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'gazzetted_no' => 'GAZ2024001',
                'gazzetted_date' => '2024-01-10',
                'advertisement_no' => 'ADV2024001',
                'advertisement_date' => '2024-01-12',
                'mode_of_advertisement' => 'Online and Newspaper',
                'publish_date_from' => '2024-01-15',
                'publish_date_to' => '2024-03-15',
                'description' => 'Annual recruitment exercise for Information Technology positions across various government departments.',
                'pre_screen_criteria' => 'Bachelor\'s degree in Computer Science or related field, minimum 2 years experience',
                'status' => 'publish',
                'created_at' => '2024-01-05 10:00:00',
                'updated_at' => '2024-01-15 09:00:00'
            ],
            2 => [
                'id' => 2,
                'org_id' => 2,
                'exercise_name' => 'Health Department Recruitment 2024',
                'gazzetted_no' => 'GAZ2024002',
                'gazzetted_date' => '2024-01-15',
                'advertisement_no' => 'ADV2024002',
                'advertisement_date' => '2024-01-17',
                'mode_of_advertisement' => 'Online and Radio',
                'publish_date_from' => '2024-01-20',
                'publish_date_to' => '2024-03-20',
                'description' => 'Recruitment for health professionals to serve in rural and urban health facilities.',
                'pre_screen_criteria' => 'Medical degree or nursing qualification, valid registration with professional body',
                'status' => 'publish',
                'created_at' => '2024-01-10 14:00:00',
                'updated_at' => '2024-01-20 08:30:00'
            ],
            3 => [
                'id' => 3,
                'org_id' => 3,
                'exercise_name' => 'Education Department Recruitment 2024',
                'gazzetted_no' => 'GAZ2024003',
                'gazzetted_date' => '2024-01-25',
                'advertisement_no' => 'ADV2024003',
                'advertisement_date' => '2024-01-27',
                'mode_of_advertisement' => 'Online and Community Notice',
                'publish_date_from' => '2024-02-01',
                'publish_date_to' => '2024-04-01',
                'description' => 'Recruitment for teaching positions in primary and secondary schools nationwide.',
                'pre_screen_criteria' => 'Teaching qualification, registration with Teaching Service Commission',
                'status' => 'publish',
                'created_at' => '2024-01-20 11:00:00',
                'updated_at' => '2024-02-01 07:45:00'
            ]
        ];

        // Check if exercise exists
        if (!isset($exercises[$exerciseId])) {
            return redirect()->to('/applicant/jobs')->with('error', 'Exercise not found or not published.');
        }

        $exercise = $exercises[$exerciseId];

        // Mock organization data based on DakoiiOrgModel structure
        $organizations = [
            1 => [
                'id' => 1,
                'org_code' => 'DICT',
                'org_name' => 'Department of ICT',
                'description' => 'Government department responsible for information and communication technology',
                'location_lock_province' => 'National Capital District',
                'location_lock_country' => 'Papua New Guinea',
                'logo_path' => '',
                'is_locationlocked' => 1,
                'postal_address' => 'P.O. Box 1234, Waigani, NCD',
                'phone_numbers' => '+************, +************',
                'email_addresses' => '<EMAIL>, <EMAIL>',
                'is_active' => 1,
                'license_status' => 'active'
            ],
            2 => [
                'id' => 2,
                'org_code' => 'DOH',
                'org_name' => 'Department of Health',
                'description' => 'Government department responsible for public health services',
                'location_lock_province' => 'National Capital District',
                'location_lock_country' => 'Papua New Guinea',
                'logo_path' => '',
                'is_locationlocked' => 0,
                'postal_address' => 'P.O. Box 5678, Boroko, NCD',
                'phone_numbers' => '+************, +************',
                'email_addresses' => '<EMAIL>, <EMAIL>',
                'is_active' => 1,
                'license_status' => 'active'
            ],
            3 => [
                'id' => 3,
                'org_code' => 'DOE',
                'org_name' => 'Department of Education',
                'description' => 'Government department responsible for education services',
                'location_lock_province' => 'National Capital District',
                'location_lock_country' => 'Papua New Guinea',
                'logo_path' => '',
                'is_locationlocked' => 0,
                'postal_address' => 'P.O. Box 9012, Waigani, NCD',
                'phone_numbers' => '+************, +************',
                'email_addresses' => '<EMAIL>, <EMAIL>',
                'is_active' => 1,
                'license_status' => 'active'
            ]
        ];

        $organization = $organizations[$exercise['org_id']];

        // Mock applied positions for current applicant
        $appliedPositions = [2, 5]; // Positions the applicant has already applied for

        // Mock position groups based on PositionsGroupModel structure
        $positionGroups = [
            1 => [
                ['id' => 1, 'exercise_id' => 1, 'group_name' => 'Senior IT Positions', 'description' => 'Senior level IT positions'],
                ['id' => 2, 'exercise_id' => 1, 'group_name' => 'Junior IT Positions', 'description' => 'Entry level IT positions']
            ],
            2 => [
                ['id' => 3, 'exercise_id' => 2, 'group_name' => 'Medical Officers', 'description' => 'Qualified medical practitioners'],
                ['id' => 4, 'exercise_id' => 2, 'group_name' => 'Nursing Staff', 'description' => 'Registered nurses and nursing assistants']
            ],
            3 => [
                ['id' => 5, 'exercise_id' => 3, 'group_name' => 'Primary Teachers', 'description' => 'Elementary school teachers'],
                ['id' => 6, 'exercise_id' => 3, 'group_name' => 'Secondary Teachers', 'description' => 'High school teachers']
            ]
        ];

        // Mock positions based on PositionsModel structure
        $allPositions = [
            // IT Department positions
            1 => [
                'id' => 1,
                'exercise_id' => 1,
                'org_id' => 1,
                'position_group_id' => 1,
                'position_reference' => 'IT-SR-001',
                'designation' => 'Senior Software Developer',
                'classification' => 'Professional',
                'award' => 'IT Professional Award Level 3',
                'location' => 'Port Moresby, NCD',
                'annual_salary' => 'K65,000 - K85,000',
                'qualifications' => 'Bachelor\'s degree in Computer Science or related field, minimum 5 years experience',
                'knowledge' => 'Advanced programming languages, database management, system architecture',
                'skills_competencies' => 'Leadership, project management, technical problem solving',
                'job_experiences' => 'Minimum 5 years in software development, team leadership experience preferred',
                'jd_filepath' => 'public/uploads/job_descriptions/senior_dev.pdf',
                'jd_texts_extracted' => 'Senior Software Developer responsible for leading development teams...',
                'remarks' => 'Urgent requirement',
                'status' => 'active',
                'has_applied' => false
            ],
            2 => [
                'id' => 2,
                'exercise_id' => 1,
                'org_id' => 1,
                'position_group_id' => 2,
                'position_reference' => 'IT-JR-001',
                'designation' => 'Junior Software Developer',
                'classification' => 'Professional',
                'award' => 'IT Professional Award Level 1',
                'location' => 'Port Moresby, NCD',
                'annual_salary' => 'K45,000 - K55,000',
                'qualifications' => 'Bachelor\'s degree in Computer Science or related field',
                'knowledge' => 'Programming fundamentals, web development, database basics',
                'skills_competencies' => 'Problem solving, teamwork, communication',
                'job_experiences' => 'Fresh graduates welcome, internship experience preferred',
                'jd_filepath' => 'public/uploads/job_descriptions/junior_dev.pdf',
                'jd_texts_extracted' => 'Junior Software Developer to join our development team...',
                'remarks' => 'Entry level position',
                'status' => 'active',
                'has_applied' => true // This position has been applied for
            ]
        ];

        // Group positions by their groups
        $groupedPositions = [];
        if (isset($positionGroups[$exerciseId])) {
            foreach ($positionGroups[$exerciseId] as $group) {
                $groupPositions = array_filter($allPositions, function($pos) use ($group) {
                    return $pos['position_group_id'] == $group['id'];
                });

                // Mark applied positions
                foreach ($groupPositions as &$position) {
                    $position['has_applied'] = in_array($position['id'], $appliedPositions);
                }

                if (!empty($groupPositions)) {
                    $groupedPositions[$group['group_name']] = array_values($groupPositions);
                }
            }
        }

        return view('applicant/applicant_exercise_details', [
            'title' => 'Exercise Details',
            'menu' => 'jobs',
            'exercise' => $exercise,
            'organization' => $organization,
            'positions' => $groupedPositions
        ]);
    }

    public function position($positionId)
    {
        // Get position details
        $position = $this->positionsModel
            ->where('id', $positionId)
            ->where('status', 'active')
            ->first();

        if (!$position) {
            return redirect()->to('/applicant/jobs')->with('error', 'Position not found or not active.');
        }

        // Get organization details
        $organization = $this->organizationsModel->find($position['org_id']);
        if (!$organization) {
            return redirect()->to('/applicant/jobs')->with('error', 'Organization not found.');
        }

        // Get exercise details
        $exercise = $this->exercisesModel
            ->where('org_id', $position['org_id'])
            ->where('status', 'publish')
            ->first();

        if (!$exercise) {
            return redirect()->to('/applicant/jobs')->with('error', 'Exercise not found or not published.');
        }

        // Get applicant details
        $applicantModel = new \App\Models\applicantsModel();
        $applicant = $applicantModel->find(session()->get('applicant_id'));

        // Check if applicant has already applied for this position
        $existingApplication = $this->applicationsModel
            ->where('applicant_id', session()->get('applicant_id'))
            ->where('position_id', $positionId)
            ->first();

        return view('applicant/applicant_position_details', [
            'title' => $position['designation'],
            'menu' => 'jobs',
            'position' => $position,
            'organization' => $organization,
            'exercise' => $exercise,
            'applicant' => $applicant,
            'has_applied' => !empty($existingApplication),
            'application' => $existingApplication
        ]);
    }

    public function apply($positionId)
    {
        try {
            if (!$this->request->isAJAX()) {
                log_message('error', 'Job application: Non-AJAX request received');
                return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
            }

            // Check if position exists and is active
            $position = $this->positionsModel
                ->where('id', $positionId)
                ->where('status', 'active')
                ->first();

            if (!$position) {
                log_message('error', "Job application: Position {$positionId} not found or not active");
                return $this->response->setJSON(['success' => false, 'message' => 'Position not found or not active']);
            }

            // Check if already applied
            $existingApplication = $this->applicationsModel
                ->where('applicant_id', session()->get('applicant_id'))
                ->where('position_id', $positionId)
                ->first();

            if ($existingApplication) {
                log_message('error', "Job application: Duplicate application for position {$positionId} by applicant " . session()->get('applicant_id'));
                return $this->response->setJSON(['success' => false, 'message' => 'You have already applied for this position']);
            }

            // Get applicant details
            $applicantModel = new \App\Models\applicantsModel();
            $applicant = $applicantModel->find(session()->get('applicant_id'));

            if (!$applicant) {
                log_message('error', "Job application: Applicant " . session()->get('applicant_id') . " not found");
                return $this->response->setJSON(['success' => false, 'message' => 'Applicant information not found']);
            }

            // Generate unique application number
            $applicationNumber = 'APP' . date('Y') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);

            // Create upload directory if it doesn't exist
            $uploadPath = FCPATH . 'public/uploads/applications';
            if (!is_dir($uploadPath)) {
                if (!mkdir($uploadPath, 0777, true)) {
                    log_message('error', "Job application: Failed to create upload directory {$uploadPath}");
                    throw new \Exception('Failed to create upload directory');
                }
            }

            // Handle file uploads
            $uploadedFiles = [];
            $files = $this->request->getFiles();

            foreach ($files['documents'] as $file) {
                if ($file->isValid() && !$file->hasMoved()) {
                    $newName = $file->getRandomName();

                    // Validate file size (100MB limit)
                    if ($file->getSizeByUnit('mb') > 100) {
                        log_message('error', "Job application: File size exceeds limit - {$file->getName()}");
                        throw new \Exception('File size must not exceed 100MB');
                    }

                    // Validate file type (PDF only)
                    if ($file->getMimeType() !== 'application/pdf') {
                        log_message('error', "Job application: Invalid file type - {$file->getName()}");
                        throw new \Exception('Only PDF files are allowed');
                    }

                    try {
                        $file->move($uploadPath, $newName);
                        $uploadedFiles[] = 'public/uploads/applications/' . $newName;
                    } catch (\Exception $e) {
                        log_message('error', "Job application: File upload failed - {$e->getMessage()}");
                        throw new \Exception('Failed to upload file: ' . $e->getMessage());
                    }
                }
            }

            if (empty($uploadedFiles)) {
                log_message('error', 'Job application: No files uploaded');
                throw new \Exception('Please upload at least one document');
            }

            // Prepare application data
            $applicationData = [
                'applicant_id' => session()->get('applicant_id'),
                'position_id' => $positionId,
                'application_number' => $applicationNumber,
                'fname' => $applicant['fname'],
                'lname' => $applicant['lname'],
                'gender' => $applicant['gender'],
                'dobirth' => $applicant['dobirth'],
                'place_of_origin' => $applicant['place_of_origin'],
                'id_photo_path' => $applicant['id_photo_path'],
                'contact_details' => $applicant['contact_details'],
                'location_address' => $applicant['location_address'],
                'id_numbers' => $applicant['id_numbers'],
                'current_employer' => $applicant['current_employer'],
                'current_position' => $applicant['current_position'],
                'current_salary' => $applicant['current_salary'],
                'citizenship' => $applicant['citizenship'],
                'marital_status' => $applicant['marital_status'],
                'date_of_marriage' => $applicant['date_of_marriage'],
                'spouse_employer' => $applicant['spouse_employer'],
                'children' => $applicant['children'],
                'offence_convicted' => $applicant['offence_convicted'],
                'referees' => $applicant['referees'],
                'how_did_you_hear_about_us' => $applicant['how_did_you_hear_about_us'],
                'signature_path' => $applicant['signature_path'],
                'publications' => $applicant['publications'],
                'awards' => $applicant['awards'],
                'status' => 'pending',
                'created_by' => session()->get('applicant_id')
            ];

            // Start transaction using model
            $this->applicationsModel->db->transStart();

            try {
                // Insert application using model
                if (!$this->applicationsModel->insert($applicationData)) {
                    $error = $this->applicationsModel->errors();
                    log_message('error', 'Job application: Database insert failed - ' . json_encode($error));
                    throw new \Exception('Database error: ' . json_encode($error));
                }

                $applicationId = $this->applicationsModel->getInsertID();

                // Insert files using model
                foreach ($uploadedFiles as $filePath) {
                    $fileData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'file_title' => 'Application Document',
                        'file_path' => $filePath,
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationFilesModel->insert($fileData)) {
                        throw new \Exception('Failed to save file information');
                    }
                }

                // Copy experiences data
                $experiences = $this->applicantExperiencesModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($experiences as $experience) {
                    $experienceData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'employer' => $experience['employer'],
                        'employer_contacts_address' => $experience['employer_contacts_address'],
                        'position' => $experience['position'],
                        'date_from' => $experience['date_from'],
                        'date_to' => $experience['date_to'],
                        'achievements' => $experience['achievements'],
                        'work_description' => $experience['work_description'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationExperiencesModel->insert($experienceData)) {
                        throw new \Exception('Failed to copy experience data');
                    }
                }

                // Copy education data
                $educationRecords = $this->applicantEducationModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($educationRecords as $education) {
                    $educationData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'institution' => $education['institution'],
                        'course' => $education['course'],
                        'date_from' => $education['date_from'],
                        'date_to' => $education['date_to'],
                        'education_level' => $education['education_level'],
                        'units' => $education['units'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationEducationModel->insert($educationData)) {
                        throw new \Exception('Failed to copy education data');
                    }
                }

                // Copy existing files data
                $existingFiles = $this->applicantFilesModel->where('applicant_id', session()->get('applicant_id'))->findAll();
                foreach ($existingFiles as $file) {
                    $existingFileData = [
                        'application_id' => $applicationId,
                        'applicant_id' => session()->get('applicant_id'),
                        'file_title' => $file['file_title'],
                        'file_description' => $file['file_description'],
                        'file_path' => $file['file_path'],
                        'created_by' => session()->get('applicant_id')
                    ];

                    if (!$this->appxApplicationFilesModel->insert($existingFileData)) {
                        throw new \Exception('Failed to copy existing file data');
                    }
                }

                // Complete transaction
                $this->applicationsModel->db->transComplete();

                if ($this->applicationsModel->db->transStatus() === false) {
                    throw new \Exception('Transaction failed');
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Application submitted successfully'
                ]);

            } catch (\Exception $e) {
                // Rollback transaction using model
                $this->applicationsModel->db->transRollback();
                log_message('error', 'Job application: Transaction failed - ' . $e->getMessage());

                // Delete uploaded files if transaction fails
                foreach ($uploadedFiles as $file) {
                    $fullPath = FCPATH . $file;
                    if (file_exists($fullPath)) {
                        unlink($fullPath);
                    }
                }

                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error submitting application: ' . $e->getMessage()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Job application: Unexpected error - ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An unexpected error occurred. Please try again later.'
            ]);
        }
    }

    /**
     * Download application file
     *
     * @param string $filename
     * @return mixed
     */
    public function downloadFile($filename)
    {
        // Security check: Verify the file belongs to this applicant
        $file = $this->appxApplicationFilesModel
            ->where('applicant_id', session()->get('applicant_id'))
            ->where('file_path', 'public/uploads/applications/' . $filename)
            ->first();

        if (!$file) {
            return $this->response->setStatusCode(403)->setBody('Access denied');
        }

        $path = FCPATH . 'public/uploads/applications/' . $filename;

        if (!file_exists($path)) {
            return $this->response->setStatusCode(404)->setBody('File not found');
        }

        return $this->response->download($path, null)->setFileName($filename);
    }

    /**
     * Display list of applications for the logged-in applicant
     */
    public function applications()
    {
        // Mock application data for UI development
        $applicationData = [
            [
                'application' => [
                    'id' => 1,
                    'application_number' => 'APP2024001',
                    'status' => 'pending',
                    'created_at' => '2024-01-15 10:30:00',
                    'updated_at' => '2024-01-15 10:30:00'
                ],
                'position' => [
                    'id' => 1,
                    'designation' => 'Software Developer',
                    'department' => 'Information Technology',
                    'salary_range' => 'K50,000 - K70,000',
                    'location' => 'Port Moresby'
                ],
                'organization' => [
                    'id' => 1,
                    'org_name' => 'Department of ICT',
                    'org_code' => 'DICT',
                    'location_lock_province' => 'National Capital District'
                ]
            ],
            [
                'application' => [
                    'id' => 2,
                    'application_number' => 'APP2024002',
                    'status' => 'shortlisted',
                    'created_at' => '2024-01-10 14:20:00',
                    'updated_at' => '2024-01-18 09:15:00'
                ],
                'position' => [
                    'id' => 2,
                    'designation' => 'Data Analyst',
                    'department' => 'Finance',
                    'salary_range' => 'K45,000 - K60,000',
                    'location' => 'Port Moresby'
                ],
                'organization' => [
                    'id' => 2,
                    'org_name' => 'Department of Finance',
                    'org_code' => 'DOF',
                    'location_lock_province' => 'National Capital District'
                ]
            ],
            [
                'application' => [
                    'id' => 3,
                    'application_number' => 'APP2024003',
                    'status' => 'rejected',
                    'created_at' => '2024-01-05 16:45:00',
                    'updated_at' => '2024-01-20 11:30:00'
                ],
                'position' => [
                    'id' => 3,
                    'designation' => 'Project Manager',
                    'department' => 'Operations',
                    'salary_range' => 'K60,000 - K80,000',
                    'location' => 'Lae'
                ],
                'organization' => [
                    'id' => 3,
                    'org_name' => 'Department of Works',
                    'org_code' => 'DOW',
                    'location_lock_province' => 'Morobe'
                ]
            ],
            [
                'application' => [
                    'id' => 4,
                    'application_number' => 'APP2024004',
                    'status' => 'interviewed',
                    'created_at' => '2024-01-20 09:15:00',
                    'updated_at' => '2024-01-25 14:20:00'
                ],
                'position' => [
                    'id' => 4,
                    'designation' => 'HR Officer',
                    'department' => 'Human Resources',
                    'salary_range' => 'K40,000 - K55,000',
                    'location' => 'Mount Hagen'
                ],
                'organization' => [
                    'id' => 4,
                    'org_name' => 'Department of Personnel Management',
                    'org_code' => 'DPM',
                    'location_lock_province' => 'Western Highlands'
                ]
            ],
            [
                'application' => [
                    'id' => 5,
                    'application_number' => 'APP2024005',
                    'status' => 'selected',
                    'created_at' => '2024-01-12 11:00:00',
                    'updated_at' => '2024-01-28 16:45:00'
                ],
                'position' => [
                    'id' => 5,
                    'designation' => 'Marketing Specialist',
                    'department' => 'Marketing',
                    'salary_range' => 'K48,000 - K65,000',
                    'location' => 'Port Moresby'
                ],
                'organization' => [
                    'id' => 5,
                    'org_name' => 'Tourism Promotion Authority',
                    'org_code' => 'TPA',
                    'location_lock_province' => 'National Capital District'
                ]
            ]
        ];

        return view('applicant/applicant_applications', [
            'title' => 'My Applications',
            'menu' => 'applications',
            'applications' => $applicationData
        ]);
    }
}