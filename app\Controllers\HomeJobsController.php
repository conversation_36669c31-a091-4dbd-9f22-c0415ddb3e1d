<?php

namespace App\Controllers;

class HomeJobsController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    /**
     * Display all published job positions
     */
    public function index()
    {
        try {
            // Get filter parameters
            $selectedOrgId = $this->request->getGet('org_id');
            $searchTerm = $this->request->getGet('search');
            $selectedClassification = $this->request->getGet('classification');
            $selectedLocation = $this->request->getGet('location');

            // Load models
            $positionsModel = new \App\Models\PositionsModel();
            $orgModel = new \App\Models\DakoiiOrgModel();

            // Build the query for positions from published exercises
            $query = $positionsModel->select('
                positions.*,
                dakoii_org.org_name,
                dakoii_org.org_code,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                exercises.publish_date_from,
                exercises.publish_date_to
            ')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('positions.status', 'active')
            ->where('exercises.status', 'published');

            // Apply filters
            if ($selectedOrgId) {
                $query->where('positions.org_id', $selectedOrgId);
            }

            if ($searchTerm) {
                $query->groupStart()
                     ->like('positions.designation', $searchTerm)
                     ->orLike('positions.position_reference', $searchTerm)
                     ->orLike('dakoii_org.org_name', $searchTerm)
                     ->orLike('exercises.exercise_name', $searchTerm)
                     ->groupEnd();
            }

            if ($selectedClassification) {
                $query->where('positions.classification', $selectedClassification);
            }

            if ($selectedLocation) {
                $query->like('positions.location', $selectedLocation);
            }

            // Get positions with pagination
            $positions = $query->orderBy('positions.created_at', 'DESC')
                              ->paginate(20); // 20 positions per page

            // Get all active organizations for filter dropdown
            $organizations = $orgModel->where('is_active', 1)
                                    ->orderBy('org_name', 'ASC')
                                    ->findAll();

            // Extract unique classifications and locations for filtering
            $allPositions = $positionsModel->select('classification, location')
                                          ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                                          ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                                          ->where('positions.status', 'active')
                                          ->where('exercises.status', 'published')
                                          ->findAll();

            $classifications = array_unique(array_filter(array_column($allPositions, 'classification')));
            $locations = array_unique(array_filter(array_column($allPositions, 'location')));

            // Get pagination object
            $pager = $positionsModel->pager;

            // Return the view with data
            return view('home/home_jobs', [
                'title' => 'Available Positions',
                'menu' => 'jobs',
                'positions' => $positions,
                'organizations' => $organizations,
                'classifications' => $classifications,
                'locations' => $locations,
                'pager' => $pager,
                'selectedOrgId' => $selectedOrgId,
                'searchTerm' => $searchTerm,
                'selectedClassification' => $selectedClassification,
                'selectedLocation' => $selectedLocation
            ]);
        } catch (\Exception $e) {
            // Log the error and show empty positions
            log_message('error', 'Error loading jobs page: ' . $e->getMessage());

            return view('home/home_jobs', [
                'title' => 'Available Positions',
                'menu' => 'jobs',
                'positions' => [],
                'organizations' => [],
                'classifications' => [],
                'locations' => [],
                'pager' => null,
                'selectedOrgId' => null,
                'searchTerm' => null,
                'selectedClassification' => null,
                'selectedLocation' => null,
                'error' => 'Unable to load positions. Please try again later.'
            ]);
        }
    }

    public function view($id = null)
    {
        if ($id === null) {
            return redirect()->to('/jobs');
        }

        // Using dummy data for UI development - replace with actual model calls later
        $positions = [
            1 => [
                'id' => 1,
                'designation' => 'Senior Software Engineer',
                'org_name' => 'Department of Information Technology',
                'orgcode' => 'DIT',
                'location' => 'Port Moresby, NCD',
                'annual_salary' => 'K85,000 - K95,000',
                'classification' => 'Level 8',
                'position_reference' => 'DIT-SE-2024-001',
                'publish_date_from' => '2024-01-15',
                'publish_date_to' => '2024-12-31',
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'qualifications' => 'Bachelor\'s degree in Computer Science, Software Engineering, or related field. Minimum 5 years of experience in software development.',
                'knowledge' => 'Proficiency in multiple programming languages including Java, Python, JavaScript. Experience with cloud platforms (AWS, Azure). Knowledge of database systems and web technologies.',
                'skills_competencies' => 'Strong problem-solving skills, excellent communication abilities, team leadership experience, project management skills.',
                'job_experiences' => 'Minimum 5 years of software development experience. Experience leading development teams. Previous work on large-scale applications.',
                'jd_filepath' => 'public/uploads/job_descriptions/senior_software_engineer.pdf',
                'status' => 'active'
            ],
            2 => [
                'id' => 2,
                'designation' => 'Project Manager',
                'org_name' => 'Department of Works',
                'orgcode' => 'DOW',
                'location' => 'Lae, Morobe',
                'annual_salary' => 'K75,000 - K85,000',
                'classification' => 'Level 7',
                'position_reference' => 'DOW-PM-2024-002',
                'publish_date_from' => '2024-01-20',
                'publish_date_to' => '2024-11-30',
                'exercise_name' => 'Infrastructure Development Exercise',
                'qualifications' => 'Bachelor\'s degree in Engineering, Project Management, or related field. PMP certification preferred.',
                'knowledge' => 'Project management methodologies, construction industry knowledge, budget management, risk assessment.',
                'skills_competencies' => 'Leadership skills, communication, stakeholder management, problem-solving, time management.',
                'job_experiences' => 'Minimum 3 years of project management experience. Experience in infrastructure or construction projects.',
                'jd_filepath' => 'public/uploads/job_descriptions/project_manager.pdf',
                'status' => 'active'
            ],
            3 => [
                'id' => 3,
                'designation' => 'Financial Analyst',
                'org_name' => 'Department of Treasury',
                'orgcode' => 'DOT',
                'location' => 'Port Moresby, NCD',
                'annual_salary' => 'K65,000 - K75,000',
                'classification' => 'Level 6',
                'position_reference' => 'DOT-FA-2024-003',
                'publish_date_from' => '2024-02-01',
                'publish_date_to' => '2024-12-15',
                'exercise_name' => 'Finance Recruitment 2024',
                'qualifications' => 'Bachelor\'s degree in Finance, Accounting, Economics, or related field. CPA qualification preferred.',
                'knowledge' => 'Financial analysis, budgeting, forecasting, financial reporting, government accounting principles.',
                'skills_competencies' => 'Analytical thinking, attention to detail, Excel proficiency, report writing, presentation skills.',
                'job_experiences' => 'Minimum 2 years of financial analysis experience. Government sector experience preferred.',
                'jd_filepath' => 'public/uploads/job_descriptions/financial_analyst.pdf',
                'status' => 'active'
            ]
        ];

        // Get the position by ID
        $position = $positions[$id] ?? null;

        if ($position === null) {
            return redirect()->to('/jobs')->with('error', 'Position not found.');
        }

        return view('home/home_job_details', [
            'title' => $position['designation'],
            'position' => $position
        ]);
    }
}
